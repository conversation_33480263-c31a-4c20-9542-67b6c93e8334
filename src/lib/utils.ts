import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const convertNumberToWords = (num: number): string => {
  if (num === 0) return "Zero Rupees only";

  const ones = [
    "",
    "One",
    "Two",
    "Three",
    "Four",
    "Five",
    "Six",
    "Seven",
    "Eight",
    "Nine",
    "Ten",
    "Eleven",
    "Twelve",
    "Thirteen",
    "Fourteen",
    "Fifteen",
    "Sixteen",
    "Seventeen",
    "Eighteen",
    "Nineteen",
  ];

  const tens = [
    "",
    "",
    "Twenty",
    "Thirty",
    "Forty",
    "Fifty",
    "Sixty",
    "Seventy",
    "Eighty",
    "Ninety",
  ];

  const convertHundreds = (n: number): string => {
    let result = "";

    if (n >= 100) {
      result += ones[Math.floor(n / 100)] + " Hundred ";
      n %= 100;
    }

    if (n >= 20) {
      result += tens[Math.floor(n / 10)] + " ";
      n %= 10;
    }

    if (n > 0) {
      result += ones[n] + " ";
    }

    return result;
  };

  let result = "";
  const crores = Math.floor(num / 10000000);
  num %= 10000000;

  const lakhs = Math.floor(num / 100000);
  num %= 100000;

  const thousands = Math.floor(num / 1000);
  num %= 1000;

  const hundreds = num;

  if (crores > 0) {
    result += convertHundreds(crores) + "Crore ";
  }

  if (lakhs > 0) {
    result += convertHundreds(lakhs) + "Lakh ";
  }

  if (thousands > 0) {
    result += convertHundreds(thousands) + "Thousand ";
  }

  if (hundreds > 0) {
    result += convertHundreds(hundreds);
  }

  return result.trim() + " Rupees only";
};

/**
 * Generates and downloads a PDF from an HTML element
 * @param elementId - The ID of the HTML element to convert to PDF
 * @param filename - The name of the PDF file to download
 */
export const generatePDF = async (
  elementId: string,
  filename: string
): Promise<void> => {
  try {
    // Import the libraries dynamically to avoid SSR issues
    const html2canvas = (await import("html2canvas")).default;
    const jsPDF = (await import("jspdf")).default;

    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error(`Element with ID "${elementId}" not found`);
    }

    // Create canvas from the element
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff",
      width: element.scrollWidth,
      height: element.scrollHeight,
      ignoreElements: (element) => {
        // Skip elements that might cause issues
        return element.classList?.contains("print:hidden") || false;
      },
      onclone: (clonedDoc) => {
        // Remove all existing stylesheets that might contain oklch
        const existingStyles = clonedDoc.querySelectorAll(
          'style, link[rel="stylesheet"]'
        );
        existingStyles.forEach((style) => style.remove());

        // Add comprehensive print-friendly styles
        const style = clonedDoc.createElement("style");
        style.textContent = `
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }

          body {
            font-family: Arial, sans-serif;
            line-height: 1.5;
            color: #000000;
            background-color: #ffffff;
          }

          #invoice-content {
            background-color: #ffffff !important;
            padding: 32px !important;
            color: #000000 !important;
            font-size: 14px;
            max-width: 100%;
          }

          h1, h2, h3, h4, h5, h6 {
            color: #000000 !important;
            font-weight: bold;
            margin-bottom: 16px;
          }

          h1 { font-size: 24px; }
          h2 { font-size: 20px; }
          h3 { font-size: 16px; }

          p {
            margin-bottom: 8px;
            color: #000000 !important;
          }

          .text-center { text-align: center; }
          .text-left { text-align: left; }
          .text-right { text-align: right; }

          .font-bold { font-weight: bold; }
          .font-semibold { font-weight: 600; }
          .font-medium { font-weight: 500; }

          .text-sm { font-size: 12px; }
          .text-xs { font-size: 11px; }
          .text-lg { font-size: 16px; }
          .text-xl { font-size: 18px; }
          .text-2xl { font-size: 24px; }

          .mb-2 { margin-bottom: 8px; }
          .mb-4 { margin-bottom: 16px; }
          .mb-6 { margin-bottom: 24px; }
          .mb-8 { margin-bottom: 32px; }
          .mt-1 { margin-top: 4px; }
          .mt-4 { margin-top: 16px; }
          .mt-8 { margin-top: 32px; }
          .mt-12 { margin-top: 48px; }

          .p-2 { padding: 8px; }
          .p-4 { padding: 16px; }
          .p-8 { padding: 32px; }

          .border {
            border: 1px solid #9ca3af;
          }

          .border-b {
            border-bottom: 1px solid #9ca3af;
          }

          .border-r {
            border-right: 1px solid #9ca3af;
          }

          .border-t {
            border-top: 1px solid #9ca3af;
          }

          .bg-gray-100 {
            background-color: #f3f4f6 !important;
          }

          .grid {
            display: table;
            width: 100%;
          }

          .grid-cols-2 {
            display: table;
            width: 100%;
            table-layout: fixed;
          }

          .grid-cols-2 > div {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 12px;
          }

          .grid-cols-2 > div:last-child {
            padding-right: 0;
            padding-left: 12px;
          }

          .gap-6 {
            margin: 12px 0;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 24px;
          }

          th, td {
            border: 1px solid #9ca3af;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
          }

          th {
            background-color: #f3f4f6;
            font-weight: bold;
          }

          .text-center,
          .text-center th,
          .text-center td {
            text-align: center !important;
          }

          .text-right,
          .text-right th,
          .text-right td {
            text-align: right !important;
          }

          .text-left,
          .text-left th,
          .text-left td {
            text-align: left !important;
          }

          .print\\:hidden {
            display: none !important;
          }
        `;
        clonedDoc.head.appendChild(style);
      },
    });

    const imgData = canvas.toDataURL("image/png");

    // Calculate PDF dimensions for US Letter size
    const imgWidth = 216; // US Letter width in mm (8.5 inches)
    const pageHeight = 279; // US Letter height in mm (11 inches)
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    // Create PDF with US Letter size
    const pdf = new jsPDF("p", "mm", "letter");
    let position = 0;

    // Add first page
    pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    // Add additional pages if content is longer than one page
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Download the PDF
    pdf.save(filename);
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error("Failed to generate PDF. Please try again.");
  }
};
