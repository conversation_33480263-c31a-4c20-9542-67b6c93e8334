import InvoicePreview from "@/components/invoice-preview";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import type { Customer, Invoice, InvoiceItem } from "@/lib/types";
import { convertNumberToWords } from "@/lib/utils";
import { customerService } from "@/services/customer";
import { invoiceService } from "@/services/invoice";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Eye, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";

const InvoiceForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [showPreview, setShowPreview] = useState(false);
  const [customer, setCustomer] = useState<Customer>({
    id: "",
    name: "",
    phone: "",
    address: "",
    email: "",
  });

  const [invoiceData, setInvoiceData] = useState({
    date: new Date().toISOString().split("T")[0],
    status: "unpaid" as "paid" | "unpaid" | "pending",
    notes: "",
  });

  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: "1",
      name: "",
      quantity: 1,
      unit: "Prs",
      price: 0,
      amount: 0,
    },
  ]);

  const [discountPercentage, setDiscountPercentage] = useState(0);

  // Fetch existing invoice if editing
  const { data: existingInvoice } = useQuery({
    queryKey: ["invoice", id],
    queryFn: () => invoiceService.getById(id!),
    enabled: !!id,
  });

  // Fetch customers for potential future use
  // const { data: customers = [] } = useQuery({
  //   queryKey: ["customers"],
  //   queryFn: customerService.getAll,
  // });

  useEffect(() => {
    if (existingInvoice) {
      setCustomer(
        existingInvoice.customer || {
          id: "",
          name: "",
          phone: "",
          address: "",
          email: "",
        }
      );
      setInvoiceData({
        date: existingInvoice.date,
        status: existingInvoice.status,
        notes: existingInvoice.notes || "",
      });
      setItems(existingInvoice.items || []);
      setDiscountPercentage(existingInvoice.discount_percentage);
    }
  }, [existingInvoice]);

  const createInvoiceMutation = useMutation({
    mutationFn: invoiceService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["invoices"] });
      toast.success("Invoice created successfully!");
      navigate("/invoices");
    },
    onError: () => {
      toast.error("Failed to create invoice. Please try again.");
    },
  });

  const updateInvoiceMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Invoice> }) =>
      invoiceService.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["invoices"] });
      queryClient.invalidateQueries({ queryKey: ["invoice", id] });
      toast.success("Invoice updated successfully!");
      navigate("/invoices");
    },
    onError: () => {
      toast.error("Failed to update invoice. Please try again.");
    },
  });

  const createCustomerMutation = useMutation({
    mutationFn: customerService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customers"] });
    },
  });

  const calculateTotals = () => {
    const subtotal = items.reduce((sum, item) => sum + item.amount, 0);
    const discount = (subtotal * discountPercentage) / 100;
    const total = subtotal - discount;
    return { subtotal, discount, total };
  };

  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      name: "",
      quantity: 1,
      unit: "Prs",
      price: 0,
      amount: 0,
    };
    setItems([...items, newItem]);
  };

  const removeItem = (itemId: string) => {
    setItems(items.filter((item) => item.id !== itemId));
  };

  const updateItem = (
    itemId: string,
    field: keyof InvoiceItem,
    value: string | number
  ) => {
    setItems(
      items.map((item) => {
        if (item.id === itemId) {
          const updatedItem = { ...item, [field]: value };
          if (field === "quantity" || field === "price") {
            updatedItem.amount = updatedItem.quantity * updatedItem.price;
          }
          return updatedItem;
        }
        return item;
      })
    );
  };

  const saveInvoice = async () => {
    if (!customer.name) {
      toast.error("Please fill in customer name.");
      return;
    }

    if (
      items.some((item) => !item.name || item.quantity <= 0 || item.price < 0)
    ) {
      toast.success("Please fill in all item details correctly.");
      return;
    }

    const { subtotal, discount, total } = calculateTotals();

    try {
      // Create or get customer
      let customerId = customer.id;
      if (!customerId) {
        const newCustomer = await createCustomerMutation.mutateAsync({
          name: customer.name,
          phone: customer.phone,
          address: customer.address || "",
          email: customer.email || "",
        });
        customerId = newCustomer.id;
      }

      const invoicePayload = {
        date: invoiceData.date,
        customer_id: customerId,
        items,
        subtotal,
        discount,
        discount_percentage: discountPercentage,
        total,
        status: invoiceData.status,
        notes: invoiceData.notes || undefined,
      };

      if (id) {
        updateInvoiceMutation.mutate({ id, data: invoicePayload });
      } else {
        createInvoiceMutation.mutate(invoicePayload);
      }
    } catch (error) {
      toast.error("Failed to save invoice. Please try again.");
      console.error(error);
    }
  };

  const { subtotal, discount, total } = calculateTotals();

  if (showPreview) {
    const previewInvoice = {
      id: id || "preview",
      invoiceNumber: existingInvoice?.invoice_number || "INV001",
      date: invoiceData.date,
      customer,
      items,
      subtotal,
      discount,
      discountPercentage: discountPercentage,
      total,
      status: invoiceData.status,
      notes: invoiceData.notes,
    };

    return (
      <InvoicePreview
        invoice={previewInvoice}
        onBack={() => setShowPreview(false)}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {id ? "Edit Invoice" : "Create Invoice"}
          </h1>
          <p className="text-gray-600">
            Fill in the details to generate an invoice
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowPreview(true)}>
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
          <Button
            onClick={saveInvoice}
            className="bg-blue-600 hover:bg-blue-700"
            disabled={
              createInvoiceMutation.isPending || updateInvoiceMutation.isPending
            }
          >
            <Save className="mr-2 h-4 w-4" />
            {createInvoiceMutation.isPending || updateInvoiceMutation.isPending
              ? "Saving..."
              : "Save Invoice"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="customerName">Customer Name *</Label>
              <Input
                id="customerName"
                value={customer.name}
                onChange={(e) =>
                  setCustomer({ ...customer, name: e.target.value })
                }
                placeholder="Enter customer name"
              />
            </div>
            <div>
              <Label htmlFor="customerPhone">Phone Number *</Label>
              <Input
                id="customerPhone"
                value={customer.phone}
                onChange={(e) =>
                  setCustomer({ ...customer, phone: e.target.value })
                }
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <Label htmlFor="customerAddress">Address</Label>
              <Textarea
                id="customerAddress"
                value={customer.address}
                onChange={(e) =>
                  setCustomer({ ...customer, address: e.target.value })
                }
                placeholder="Enter customer address"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="customerEmail">Email</Label>
              <Input
                id="customerEmail"
                type="email"
                value={customer.email}
                onChange={(e) =>
                  setCustomer({ ...customer, email: e.target.value })
                }
                placeholder="Enter customer email"
              />
            </div>
          </CardContent>
        </Card>

        {/* Invoice Details */}
        <Card>
          <CardHeader>
            <CardTitle>Invoice Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="invoiceDate">Invoice Date</Label>
              <Input
                id="invoiceDate"
                type="date"
                value={invoiceData.date}
                onChange={(e) =>
                  setInvoiceData({ ...invoiceData, date: e.target.value })
                }
              />
            </div>

            <div>
              <Label htmlFor="status">Payment Status</Label>
              <Select
                value={invoiceData.status}
                onValueChange={(value: "paid" | "unpaid" | "pending") =>
                  setInvoiceData({ ...invoiceData, status: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="unpaid">Unpaid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Invoice Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>₹{subtotal.toFixed(2)}</span>
            </div>
            <div>
              <Label htmlFor="discount">Discount (%)</Label>
              <Input
                id="discount"
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={discountPercentage}
                onChange={(e) =>
                  setDiscountPercentage(parseFloat(e.target.value) || 0)
                }
              />
            </div>
            <div className="flex justify-between">
              <span>Discount Amount:</span>
              <span>₹{discount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between font-bold text-lg border-t pt-2">
              <span>Total:</span>
              <span>₹{total.toFixed(2)}</span>
            </div>
            <div className="text-sm text-gray-600">
              <strong>Amount in Words:</strong>
              <br />
              {convertNumberToWords(total)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Items */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Invoice Items</CardTitle>
            <Button onClick={addItem} size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Item
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {items.map((item) => (
              <div
                key={item.id}
                className="grid grid-cols-12 gap-4 items-end p-4 bg-gray-50 rounded-lg"
              >
                <div className="col-span-4">
                  <Label>Item Name</Label>
                  <Input
                    value={item.name}
                    onChange={(e) =>
                      updateItem(item.id, "name", e.target.value)
                    }
                    placeholder="Enter item name"
                  />
                </div>
                <div className="col-span-2">
                  <Label>Quantity</Label>
                  <Input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) =>
                      updateItem(
                        item.id,
                        "quantity",
                        parseInt(e.target.value) || 1
                      )
                    }
                  />
                </div>
                <div className="col-span-1">
                  <Label>Unit</Label>
                  <Select
                    value={item.unit}
                    onValueChange={(value) =>
                      updateItem(item.id, "unit", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Prs">Prs</SelectItem>
                      <SelectItem value="Pcs">Pcs</SelectItem>
                      <SelectItem value="Kg">Kg</SelectItem>
                      <SelectItem value="Set">Set</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-2">
                  <Label>Price (₹)</Label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.price}
                    onChange={(e) =>
                      updateItem(
                        item.id,
                        "price",
                        parseFloat(e.target.value) || 0
                      )
                    }
                  />
                </div>
                <div className="col-span-2">
                  <Label>Amount (₹)</Label>
                  <Input
                    value={item.amount.toFixed(2)}
                    readOnly
                    className="bg-gray-100"
                  />
                </div>
                <div className="col-span-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeItem(item.id)}
                    disabled={items.length === 1}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={invoiceData.notes}
            onChange={(e) =>
              setInvoiceData({ ...invoiceData, notes: e.target.value })
            }
            placeholder="Add any additional notes or terms..."
            rows={3}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceForm;
